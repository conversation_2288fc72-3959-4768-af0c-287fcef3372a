"""
AI client module for VPS AI Admin Backend.
Handles Gemini AI initialization, chat management, and response processing.
"""

import asyncio
import sys
import re
from typing import Optional, List, Tuple
import google.generativeai as genai
from google import genai as google_genai
from google.genai import types

from config import Config
from models import SSHResult


class AIClient:
    """AI client for interacting with Gemini AI."""

    def __init__(self, config: Config):
        self.config = config
        self.model = None
        self.intent_model = None
        self.flash_client = None  # For Flash 2.5 with thinking
        self.intent_cache = {}  # Cache for intent detection
        self._initialize_gemini()
        self._initialize_flash_client()

    def _initialize_gemini(self):
        """Initialize Gemini AI client."""
        print(f"Initializing Gemini client with model: {self.config.GEMINI_MODEL_NAME}...")
        try:
            genai.configure(api_key=self.config.GEMINI_API_KEY)
            self.model = genai.GenerativeModel(self.config.GEMINI_MODEL_NAME)
            print("Gemini client configured successfully.")
        except Exception as e:
            print(f"FATAL: Error configuring Gemini client: {e}")
            sys.exit(1)

    def _initialize_flash_client(self):
        """Initialize Flash 2.5 client for VPS tasks with thinking."""
        try:
            self.flash_client = google_genai.Client(api_key=self.config.GEMINI_API_KEY)
            print(f"Flash 2.5 client initialized: {self.config.FLASH_MODEL_NAME}")
        except Exception as e:
            print(f"WARNING: Failed to initialize Flash 2.5 client: {e}")
            self.flash_client = None

    def create_chat_session(self, initial_history: List[genai.types.ContentDict]):
        """Create a new chat session with initial history."""
        return self.model.start_chat(history=initial_history)

    def create_initial_history(self, system_info: str, initial_prompt: str) -> List[genai.types.ContentDict]:
        """Create initial chat history with system info and user prompt."""
        system_context_message = genai.types.ContentDict(
            role='user',  # Using 'user' role for system context
            parts=[genai.types.PartDict(text=f"[Initial System Info:\n{system_info}\nEnd System Info]")]
        )
        user_prompt_message = genai.types.ContentDict(
            role='user',
            parts=[genai.types.PartDict(text=initial_prompt)]
        )
        return [system_context_message, user_prompt_message]

    def format_chat_history(self, chat) -> str:
        """Format chat history for AI prompts."""
        history_string = "Conversation History:\n"

        if chat and chat.history:
            for i, msg in enumerate(chat.history):
                try:
                    # Get role and parts from message
                    role = getattr(msg, 'role', 'unknown')
                    parts = getattr(msg, 'parts', [])

                    # Determine role label
                    role_label = "User" if role == "user" else "AI" if role == "model" else "System"

                    content = "[No text found in parts]"
                    if parts:
                        texts = []
                        for part in parts:
                            # Extract text from part
                            text = None
                            if hasattr(part, 'text'):
                                text = getattr(part, 'text', None)
                            elif isinstance(part, dict):
                                text = part.get('text', None)
                            elif isinstance(part, str):
                                text = part

                            texts.append(text or '')

                        # Join non-empty texts
                        content = " ".join(filter(lambda x: x is not None, texts)).strip()
                        if not content:
                            content = "[Empty content in parts]"

                    # Ensure content is not None
                    if content is None:
                        content = ""

                    history_string += f"{role_label}: {content}\n"

                except Exception as e:
                    print(f"WARNING: Skipping message index {i} in history due to formatting error: {type(e).__name__} - {e}")
                    continue

        return history_string

    def _initialize_intent_model(self):
        """Initialize the intent detection model."""
        try:
            self.intent_model = genai.GenerativeModel(self.config.INTENT_MODEL_NAME)
            print(f"Intent detection model initialized: {self.config.INTENT_MODEL_NAME}")
        except Exception as e:
            print(f"WARNING: Failed to initialize intent model, falling back to main model: {e}")
            self.intent_model = self.model

    def _fallback_intent_detection(self, user_message: str, history_string: str) -> str:
        """Fallback keyword-based intent detection when LLM fails."""
        user_message_lower = user_message.lower().strip()

        # Admin indicators (more comprehensive)
        admin_indicators = [
            'install', 'update', 'upgrade', 'check', 'status', 'restart', 'start', 'stop',
            'service', 'process', 'memory', 'disk', 'cpu', 'log', 'error', 'fix',
            'configure', 'setup', 'deploy', 'monitor', 'backup', 'restore', 'permission',
            'user', 'group', 'network', 'firewall', 'security', 'package', 'apt', 'yum',
            'systemctl', 'sudo', 'root', 'server', 'database', 'nginx', 'apache',
            'cron', 'crontab', 'list', 'show', 'display', 'view', 'ps', 'kill', 'jobs',
            'running', 'active', 'enabled', 'disabled', 'port', 'listen', 'connection',
            'git', 'clone', 'pull', 'push', 'commit', 'branch', 'docker', 'container',
            'host', 'deploy', 'build', 'compile', 'run', 'execute', 'download', 'upload'
        ]

        # Casual indicators
        casual_indicators = [
            'hi', 'hello', 'hey', 'how are you', 'what can you do', 'who are you',
            'thanks', 'thank you', 'bye', 'goodbye', 'good morning', 'good evening'
        ]

        # Check for admin indicators first
        if any(indicator in user_message_lower for indicator in admin_indicators):
            return "admin"

        # Check for casual indicators
        if any(indicator in user_message_lower for indicator in casual_indicators):
            return "casual"

        # Check history context
        if any(term in history_string.lower() for term in ["systemctl", "sudo", "install", "crontab"]):
            return "admin"

        return "casual"

    async def _detect_user_intent_with_percentage(self, user_message: str, history_string: str) -> Tuple[str, int]:
        """Detect user intent with percentage confidence using Gemma-27b."""

        # Check cache first
        cache_key = user_message.lower().strip()
        if cache_key in self.intent_cache:
            print(f"DEBUG: Using cached intent for: '{user_message}'")
            return self.intent_cache[cache_key]

        # Skip LLM if disabled in config
        if not self.config.USE_LLM_INTENT:
            print(f"DEBUG: LLM intent detection disabled, using keyword fallback")
            intent = self._fallback_intent_detection(user_message, history_string)
            result = (intent, 100 if intent == "admin" else 0)  # Binary fallback
            self.intent_cache[cache_key] = result
            return result

        # Try Gemma-27b based detection with percentage
        try:
            # Use the prompt format from the image
            intent_prompt = f"""you are a vps admin assistant, if a user has a chatting intent you can respond with:
#Chat:(normal chat message/response)
if the user has an intent of administering his vps or doing a system task or running a terminal command(s) you can respond with:
#VPS:(0%-100%) (the 0%-100% is the the percentage of how hard the task given)
note: do not respond with anything after these tags #Chat: or #VPS, it will not count.

user: {user_message}"""

            # Use intent model for classification
            if not hasattr(self, 'intent_model') or self.intent_model is None:
                self._initialize_intent_model()

            response = await asyncio.to_thread(self.intent_model.generate_content, intent_prompt)
            response_text = response.text.strip()

            print(f"DEBUG: Gemma-27b raw response: '{response_text}'")

            # Parse the response
            if response_text.startswith("#Chat:"):
                chat_response = response_text[6:].strip()  # Remove "#Chat:" prefix
                result = ("chat", 0, chat_response)
                print(f"DEBUG: Detected chat intent with response: '{chat_response[:50]}...'")
            elif response_text.startswith("#VPS:"):
                vps_part = response_text[5:].strip()  # Remove "#VPS:" prefix
                # Extract percentage using regex
                percentage_match = re.search(r'(\d+)%', vps_part)
                if percentage_match:
                    percentage = int(percentage_match.group(1))
                    result = ("vps", percentage, None)
                    print(f"DEBUG: Detected VPS intent with {percentage}% difficulty")
                else:
                    print(f"WARNING: Could not parse percentage from VPS response: '{vps_part}'")
                    result = ("vps", 70, None)  # Default percentage
            else:
                print(f"WARNING: Unexpected response format: '{response_text}', using fallback")
                raise ValueError("Invalid response format")

            # Cache the result
            self.intent_cache[cache_key] = result
            return result

        except Exception as e:
            print(f"WARNING: Gemma intent detection failed ({e}), using keyword fallback")
            # Use fallback keyword-based detection
            intent = self._fallback_intent_detection(user_message, history_string)
            result = (intent, 100 if intent == "admin" else 0, None)
            print(f"DEBUG: Fallback detected intent '{intent}' for message: '{user_message}'")
            # Cache the fallback result too
            self.intent_cache[cache_key] = result
            return result

    async def _detect_user_intent(self, user_message: str, history_string: str) -> str:
        """Legacy method for backward compatibility - returns just the intent type."""
        intent_type, percentage, chat_response = await self._detect_user_intent_with_percentage(user_message, history_string)

        # Map new intent types to legacy format
        if intent_type == "chat":
            return "casual"
        elif intent_type == "vps":
            return "admin"
        else:
            return intent_type

    async def build_ai_prompt(self, history_string: str, user_message: str, system_info: str,
                       last_command_outcome: Optional[SSHResult] = None) -> Tuple[str, str, int, Optional[str]]:
        """Build comprehensive AI prompt with context and instructions.

        Returns:
            Tuple[prompt, intent_type, percentage, chat_response]
        """

        # Add current user message to history
        full_history = history_string + f"User: {user_message}\nAI: "

        # Detect user intent using enhanced LLM detection
        intent_type, percentage, chat_response = await self._detect_user_intent_with_percentage(user_message, history_string)

        # If it's a chat intent, return the chat response directly
        if intent_type == "chat" and chat_response:
            return chat_response, intent_type, percentage, chat_response

        # For VPS tasks, build the technical prompt
        if intent_type == "vps":
            # System administration prompt (original technical prompt)
            context_block = (
                f"--- System Information ---\n{system_info}\n--- End System Information ---\n\n"
                "--- Enhanced Instructions ---\n"
                "You are an advanced Linux system administrator assistant with enhanced capabilities for task management, security awareness, and intelligent automation. "
                "You manage the server described in 'System Information' above via SSH non-interactively.\n\n"

                "**Core Principles:**\n"
                "1. **Task Decomposition**: Break complex tasks into logical steps and track progress\n"
                "2. **Security First**: Always consider security implications of commands\n"
                "3. **Error Recovery**: Intelligently handle failures and suggest alternatives\n"
                "4. **Context Awareness**: Use conversation history and system state for decisions\n"
                "5. **Non-Interactive**: Only propose commands that don't require user interaction\n\n"

                "**Security Guidelines:**\n"
                "- **CRITICAL**: Never propose commands that could compromise system security\n"
                "- Avoid commands that disable firewalls, security services, or authentication\n"
                "- Be cautious with file permissions, especially for system files\n"
                "- Warn about potentially dangerous operations (data deletion, service disruption)\n"
                "- Prefer specific package names over wildcards in installations\n"
                "- Always use package managers rather than downloading/compiling from source\n\n"

                "**Enhanced Task Management:**\n"
                "- For complex tasks, estimate total steps and track current progress\n"
                "- Provide clear descriptions of what each command accomplishes\n"
                "- If a command fails, analyze the error and suggest specific fixes\n"
                "- Consider dependencies and prerequisites before proposing commands\n"
                "- Group related commands logically (e.g., update package lists before installing)\n\n"

                "**System-Specific Commands:**\n"
                "- Use System Information to determine correct package managers and service managers\n"
                "- For systemd systems: use `systemctl` commands\n"
                "- For older systems: use `service` commands\n"
                "- For Debian/Ubuntu: use `apt` or `apt-get`\n"
                "- For RHEL/CentOS/Fedora: use `yum` or `dnf`\n\n"

                "**Package Management Protocol:**\n"
                "1. **Always check if package is installed first**: `dpkg -s <package>` or `rpm -q <package>`\n"
                "2. **Update package lists if needed**: `apt update` or `yum check-update`\n"
                "3. **Install only if not present**: `apt install -y <package>` or `yum install -y <package>`\n"
                "4. **Verify installation**: Check service status or command availability\n\n"

                "**Service Management Protocol:**\n"
                "1. **Check current status**: `systemctl status <service>` or `service <service> status`\n"
                "2. **Start/stop/restart as needed**: `systemctl start/stop/restart <service>`\n"
                "3. **Enable for boot if required**: `systemctl enable <service>`\n"
                "4. **Verify operation**: Check logs or test functionality\n\n"
            )
        else:
            # This shouldn't happen since we handle chat above, but fallback
            context_block = f"--- System Information ---\n{system_info}\n--- End System Information ---\n\n"

        # Add last command outcome and analysis task for VPS tasks
        outcome_summary = ""
        analysis_task_prompt = ""

        if intent_type == "vps":
            # Original admin mode logic
            if last_command_outcome:
                # Provide outcome
                cmd = last_command_outcome.command or "N/A"
                success = last_command_outcome.success
                exit_status = last_command_outcome.exit_status
                stderr = last_command_outcome.stderr.strip()
                stdout = last_command_outcome.stdout.strip()

                outcome_summary = f"\n--- Outcome of Last Command Attempt ---\nCommand: '{cmd}'\nSuccess: {success}\nExit Code: {exit_status}"
                if stdout:
                    outcome_summary += f"\nStdout:\n---\n{stdout[:1000]}\n---"
                if stderr:
                    outcome_summary += f"\nStderr:\n---\n{stderr[:1000]}\n---"
                outcome_summary += "\n--- End Outcome ---"

                # Define analysis task based on outcome
                if success:
                    analysis_task_prompt = (
                        "\n\n**Analysis Task:** The previous command succeeded. "
                        "1. Review the **latest** 'User:' message in the Conversation History that led to this command execution. "
                        "2. Examine the command's output (Stdout/Stderr above). "
                        "3. If this output successfully fulfills the user's latest request, respond with **exactly** 'TASK_COMPLETE'. "
                        "4. Otherwise, determine the *single next non-interactive command* needed to continue fulfilling that request, OR ask a clarifying question using 'QUESTION:'."
                    )
                elif not success and stderr:
                    analysis_task_prompt = (
                        "\n\n**Analysis Task:** The previous command failed. Analyze the Stderr output above. "
                        "Propose the *single next command* to fix the specific error (e.g., install missing package, correct syntax, fix permissions). "
                        "If the error is unclear or needs user input, ask a specific question using 'QUESTION:'. "
                        "If the error indicates a fundamental problem you cannot solve, explain briefly using 'QUESTION:' why you cannot proceed."
                    )
                else:
                    analysis_task_prompt = "\n\n**Analysis Task:** The previous command failed without specific stderr output. Determine the next logical step to address the failure or ask for clarification using 'QUESTION:'."
            else:
                outcome_summary = "\n\n(No previous command executed in this turn.)"
                analysis_task_prompt = (
                    "\n\n**Analysis Task:** This is the first interaction after the initial system scan, or the user provided input without confirming a command. "
                    "1. Analyze the **latest** 'User:' message in the Conversation History. "
                    "2. **Crucially, the 'System Information' provided earlier is background context.** Even if it contains related data (like service or package lists), the user's request likely requires you to execute a *new command* to get the *current* status or perform an action (e.g., 'check running services' requires running a command like `systemctl status` or similar, not just looking at the initial scan). "
                    "3. Determine the *single next non-interactive command* required to actively address the user's request. "
                    "4. **Only respond with 'TASK_COMPLETE' if the user's latest message explicitly asks for nothing, says 'done', 'cancel', or similar.** Do NOT assume the task is complete just because context information exists. "
                    "5. If the request is unclear, ask a clarifying question using 'QUESTION:'."
                )

            # Define command generation constraints for admin mode
            constraints = (
                "\n\n**Response Requirements:**\n"
                "1. Determine the *single next specific non-interactive shell command* to fulfill the user's latest request OR to correct a previous error based on that latest request.\n"
                "2. **CRITICAL: AVOID interactive commands** (like `nano`, `vim`, `crontab -e`). Use non-interactive alternatives (`sed`, `echo`, `crontab file`, `apt-get -y`, `yum -y`, etc.).\n"
                "3. Use the system information (OS detected, if available in history) for correct commands (apt vs yum etc.).\n"
                "4. Output *only* the shell command, OR 'TASK_COMPLETE', OR 'QUESTION: your specific question/explanation'.\n"
                "5. Ensure commands needing root privileges use 'sudo '.\n"
                "6. **Adhere strictly to the package check rule:** Check first (using `dpkg -s` or `rpm -q`), then install only if the check failed.\n"
                "7. **Adhere strictly to the service management rule:** Prefer `systemctl` if available based on OS info or check, otherwise use `service`.\n"
                "Response format must be: Just the command, 'TASK_COMPLETE', or 'QUESTION: ...'."
            )

        # Combine into full prompt
        full_prompt = full_history + context_block + outcome_summary + analysis_task_prompt + constraints
        return full_prompt, intent_type, percentage, chat_response

    def _calculate_thinking_budget(self, percentage: int) -> int:
        """Calculate thinking budget based on percentage (50%-100% = 0-24576)."""
        if percentage < self.config.MIN_THINKING_PERCENTAGE:
            return 0

        # Map 50%-100% to 0-24576
        normalized = (percentage - self.config.MIN_THINKING_PERCENTAGE) / (100 - self.config.MIN_THINKING_PERCENTAGE)
        thinking_budget = int(normalized * 24576)

        print(f"DEBUG: Percentage {percentage}% -> Thinking budget: {thinking_budget}")
        return thinking_budget

    async def generate_response_with_thinking(self, task_id: str, prompt: str, percentage: int, timeout: int = 120) -> str:
        """Generate AI response using Flash 2.5 with thinking budget based on percentage."""
        if not self.flash_client or not self.config.USE_THINKING:
            print("DEBUG: Flash client not available or thinking disabled, falling back to regular generation")
            return await self.generate_response(task_id, prompt, timeout)

        try:
            thinking_budget = self._calculate_thinking_budget(percentage)

            print(f"DEBUG: Using Flash 2.5 with thinking budget {thinking_budget} for {percentage}% difficulty task")

            contents = [
                types.Content(
                    role="user",
                    parts=[types.Part.from_text(text=prompt)],
                ),
            ]

            # Configure generation with thinking if budget > 0
            if thinking_budget > 0:
                generate_content_config = types.GenerateContentConfig(
                    thinking_config=types.ThinkingConfig(
                        thinking_budget=thinking_budget,
                    ),
                    response_mime_type="text/plain",
                )
            else:
                generate_content_config = types.GenerateContentConfig(
                    response_mime_type="text/plain",
                )

            # Use asyncio.to_thread to run the sync generator in a thread
            final_response = await asyncio.wait_for(
                asyncio.to_thread(lambda: "".join([
                    chunk.text for chunk in self.flash_client.models.generate_content_stream(
                        model=self.config.FLASH_MODEL_NAME,
                        contents=contents,
                        config=generate_content_config,
                    ) if chunk.text
                ])),
                timeout=timeout
            )

            print(f"DEBUG: Flash 2.5 response received. Length: {len(final_response)} chars")
            return final_response.strip()

        except asyncio.TimeoutError:
            print(f"ERROR: Flash 2.5 API call timed out after {timeout} seconds.")
            raise TimeoutError(f"AI interaction timed out after {timeout} seconds.")
        except Exception as e:
            print(f"ERROR: Flash 2.5 generation failed ({e}), falling back to regular model")
            return await self.generate_response(task_id, prompt, timeout)

    async def generate_response(self, task_id: str, prompt: str, timeout: int = 120) -> str:
        """Generate AI response with timeout using direct model call."""
        try:
            print(f"DEBUG: Sending prompt to Gemini ({self.config.GEMINI_MODEL_NAME}). Prompt length: {len(prompt)} chars. Timeout: {timeout}s")

            # Use direct model call for better control
            response = await asyncio.wait_for(
                asyncio.to_thread(self.model.generate_content, prompt),
                timeout=timeout
            )

            print("DEBUG: Received response object from Gemini.")

            # Check if response has text
            if not response or not hasattr(response, 'text'):
                print(f"WARNING: AI response missing expected text content. Response object: {response}")
                safety_feedback = getattr(response, 'prompt_feedback', None)
                error_content = "AI response was empty or blocked."
                if safety_feedback:
                    error_content += f" Safety Feedback: {safety_feedback}"
                raise ValueError(error_content)

            response_text = response.text.strip()
            print(f"DEBUG: Raw Gemini Response Text: '{response_text[:150]}...'")
            return response_text

        except asyncio.TimeoutError:
            print(f"ERROR: Gemini API call timed out after {timeout} seconds.")
            raise TimeoutError(f"AI interaction timed out after {timeout} seconds.")
        except Exception as e:
            print(f"ERROR: Unexpected error in Gemini API call: {type(e).__name__}: {e}")
            import traceback
            print(f"ERROR: Traceback: {traceback.format_exc()}")
            raise

    def update_chat_history(self, chat, user_message: str, ai_response: str):
        """Manually update chat history with user message and AI response."""
        try:
            # Check if the last message in history is already this user message to avoid duplicates
            if chat.history:
                last_msg = chat.history[-1]
                if hasattr(last_msg, 'role'):
                    last_msg_is_user = last_msg.role == 'user' and last_msg.parts[0].text == user_message
                elif isinstance(last_msg, dict):
                    last_msg_is_user = last_msg.get('role') == 'user' and last_msg.get('parts', [{}])[0].get('text') == user_message
                else:
                    last_msg_is_user = False
            else:
                last_msg_is_user = False

            # Append user message if not duplicate
            if user_message and not last_msg_is_user:
                chat.history.append(genai.types.ContentDict(
                    role='user',
                    parts=[genai.types.PartDict(text=user_message)]
                ))
                print("DEBUG: Appended user message to history.")
            elif last_msg_is_user:
                print("DEBUG: Skipping duplicate user message append to history.")

            # Append AI response
            if ai_response:
                chat.history.append(genai.types.ContentDict(
                    role='model',
                    parts=[genai.types.PartDict(text=ai_response)]
                ))
                print("DEBUG: Appended AI response ('model' role) to history.")
            else:
                print("DEBUG: Skipping AI response append to history (empty action).")

        except Exception as hist_update_err:
            print(f"WARNING: Failed to manually update history: {hist_update_err}")

    async def generate_summary(self, chat, prompt: str = None) -> str:
        """Generate a task summary."""
        if not prompt:
            prompt = "Provide a brief summary of what was accomplished in this task, including any important outcomes or next steps."

        summary_response = await asyncio.to_thread(chat.send_message, prompt)
        return summary_response.text
