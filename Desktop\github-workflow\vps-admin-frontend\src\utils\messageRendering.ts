/**
 * Message rendering utilities for VPS Admin Chat
 */

import React from 'react';
import {
  <PERSON>ert<PERSON>riangle,
  CheckCircle,
  XCircle,
  Info,
  HelpCircle,
  Sparkles,
  ChevronDown
} from 'lucide-react';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { SSHInfo } from '../types';
import TerminalOutputWithModal from '../components/TerminalOutputWithModal';

/**
 * Create message content based on message type and data
 */
export const createMessageContent = {
  error: (content: string) => (
    React.createElement(React.Fragment, null,
      React.createElement(AlertTriangle, { size: 14, className: "inline mr-1 text-red-500" }),
      content
    )
  ),

  question: (content: string) => (
    React.createElement(React.Fragment, null,
      React.createElement(HelpCircle, { size: 14, className: "inline mr-1 text-purple-500" }),
      content
    )
  ),

  commandRequest: (content: string) => (
    React.createElement('div', { className: 'w-full' },
      React.createElement('p', { className: "mb-2 font-medium" }, "🤖 AI proposes the following command:"),
      React.createElement(SyntaxHighlighter, {
        language: "bash",
        style: vscDarkPlus,
        className: "rounded text-sm w-full code-block-wrapper",
        children: content
      })
    )
  ),

  taskEnd: (content: string) => (
    React.createElement(React.Fragment, null,
      React.createElement(CheckCircle, { size: 14, className: "inline mr-1 text-green-500" }),
      content
    )
  ),

  info: (content: string) => (
    React.createElement(React.Fragment, null,
      React.createElement(Info, { size: 14, className: "inline mr-1 text-blue-500" }),
      content
    )
  ),

  warning: (content: string) => (
    React.createElement(React.Fragment, null,
      React.createElement(AlertTriangle, { size: 14, className: "inline mr-1 text-yellow-500" }),
      content
    )
  ),

  sshOutput: (sshInfo: SSHInfo) => (
    React.createElement(TerminalOutputWithModal, { sshInfo })
  ),

  summary: (content: string) => (
    React.createElement(React.Fragment, null,
      React.createElement(Sparkles, { size: 14, className: "inline mr-1 text-indigo-500" }),
      content
    )
  )
};

/**
 * Get placeholder text based on UI state
 */
export const getInputPlaceholder = (
  isTaskActive: boolean,
  isWaiting: boolean,
  showConfirmation: boolean,
  isAwaitingAnswer: boolean
): string => {
  if (!isTaskActive) return "Enter initial task to start...";
  if (isWaiting) return "Waiting for AI response...";
  if (showConfirmation) return "Confirm using buttons above";
  if (isAwaitingAnswer) return "Type your answer to the question...";
  return "Type your message or next request...";
};
