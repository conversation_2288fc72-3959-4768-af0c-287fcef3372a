# VPS Admin Enhanced Intent Detection System

## Overview

This implementation enhances the VPS Admin system with a sophisticated intent detection mechanism using Gemma-27b and Flash 2.5 with thinking capabilities, similar to the system shown in your image.

## Key Features

### 1. Enhanced Intent Detection (Gemma-27b)
- **Model**: `models/gemma-3-27b-it`
- **Prompt Format**: Matches the format from your image
- **Response Types**:
  - `#Chat:(response)` - For casual conversation
  - `#VPS:(percentage%)` - For VPS administration tasks

### 2. Thinking Budget System (Flash 2.5)
- **Model**: `gemini-2.5-flash-preview-05-20`
- **Thinking Budget**: 50%-100% difficulty maps to 0-24576 thinking budget
- **Threshold**: Only uses thinking when percentage ≥ 50%
- **Formula**: `(percentage - 50) / 50 * 24576`

### 3. Response Routing
- **Chat Intents**: Direct response from Gemma-27b (no additional AI call)
- **VPS Tasks ≥50%**: Flash 2.5 with thinking budget
- **VPS Tasks <50%**: Regular Gemini model
- **API Optimization**: Minimized calls through intelligent routing

## Implementation Details

### Files Modified

1. **`backend/config.py`**
   - Added Flash 2.5 model configuration
   - Added thinking budget settings
   - Added minimum thinking percentage threshold

2. **`backend/ai_client.py`**
   - New `_detect_user_intent_with_percentage()` method
   - New `generate_response_with_thinking()` method
   - New `_calculate_thinking_budget()` method
   - Enhanced prompt building with intent-aware routing

3. **`backend/stream_processor.py`**
   - Updated to handle new intent detection format
   - Intelligent model selection based on intent and percentage
   - Direct chat response handling

4. **`backend/.env`**
   - Updated model configurations
   - Added thinking-related settings

### Configuration (.env)

```env
# Intent detection using Gemma-27b
INTENT_MODEL=models/gemma-3-27b-it

# Flash 2.5 for VPS tasks with thinking
FLASH_MODEL=gemini-2.5-flash-preview-05-20

# Enable thinking for VPS tasks
USE_THINKING=true

# Minimum percentage to use thinking (50%-100% = 0-24576 thinking budget)
MIN_THINKING_PERCENTAGE=50
```

## Usage Examples

### Chat Intent
**User**: "Hi there! How are you?"
**Gemma Response**: `#Chat:Hello! I'm doing well, thank you for asking. I'm here to help you with server administration tasks or just have a friendly conversation. How can I assist you today?`
**System Action**: Uses direct chat response, no additional AI call

### VPS Intent (Low Complexity)
**User**: "Check nginx status"
**Gemma Response**: `#VPS:30%`
**System Action**: Uses regular Gemini model (below 50% threshold)

### VPS Intent (High Complexity)
**User**: "Deploy a complex web application with database, load balancer, and SSL"
**Gemma Response**: `#VPS:85%`
**System Action**: Uses Flash 2.5 with thinking budget of ~17,203

## Benefits

1. **Intelligent Resource Usage**: Only uses expensive thinking for complex tasks
2. **Faster Chat Responses**: Direct responses for casual conversation
3. **Better Task Handling**: Thinking budget scales with task complexity
4. **API Optimization**: Minimized calls through smart routing
5. **Backward Compatibility**: Legacy intent detection still works

## Testing

Run the test script to verify the implementation:

```bash
cd backend
python test_new_intent_system.py
```

## Future Enhancements

1. **Dynamic Percentage Adjustment**: Learn from task outcomes to adjust percentages
2. **Context-Aware Thinking**: Adjust thinking budget based on conversation history
3. **Performance Metrics**: Track thinking budget effectiveness
4. **User Preferences**: Allow users to set thinking thresholds

## Notes

- The system gracefully falls back to regular models if Flash 2.5 is unavailable
- Intent detection is cached to avoid repeated API calls for similar messages
- The thinking budget calculation ensures optimal resource utilization
- All existing functionality is preserved while adding new capabilities

This implementation provides a sophisticated, efficient, and scalable intent detection system that matches your requirements while optimizing API usage and response quality.
