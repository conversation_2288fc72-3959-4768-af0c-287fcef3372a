# Thinking Budget Implementation Documentation

## Overview

This document describes the implementation of the thinking budget system for the VPS Admin AI assistant, which uses Gemma-27b for intent detection and Flash 2.5 with thinking capabilities for complex VPS tasks.

## Architecture

### Intent Detection Flow

1. **User Message** → **Gemma-27b Intent Detection** → **Route to Handler**
2. **Chat Intent** → Direct response without planning
3. **VPS Intent** → Planning with thinking budget → Execution

### Components

#### 1. Intent Detection (Gemma-27b)
- **Model**: `models/gemma-3-27b-it` (configurable via `INTENT_MODEL` env var)
- **Purpose**: Classify user messages as chat or VPS tasks
- **Output Format**:
  - `#Chat:(response message)` - Direct chat response
  - `#VPS:(0%-100%)` - VPS task with difficulty percentage

#### 2. Thinking Budget Calculation
- **Trigger**: Percentage > 50% (configurable via `MIN_THINKING_PERCENTAGE`)
- **Formula**: `(percentage - 50) / 50 * 24576`
- **Range**: 50%-100% maps to 0-24576 thinking budget
- **Model**: `gemini-2.5-flash-preview-05-20`

#### 3. Task Orchestrator Integration
- **Phase 0**: Intent detection before planning
- **Phase 1**: Planning with thinking budget (if VPS task)
- **Phase 2**: Execution as normal

## Implementation Details

### Configuration Variables

```env
# Intent Detection
INTENT_MODEL=models/gemma-3-27b-it
USE_LLM_INTENT=true

# Thinking Budget
FLASH_MODEL=gemini-2.5-flash-preview-05-20
USE_THINKING=true
MIN_THINKING_PERCENTAGE=50
```

### Code Structure

#### TaskOrchestrator.execute_task()
```python
# Phase 0: Intent Detection
intent_type, percentage, chat_response = await self.ai_client._detect_user_intent_with_percentage(
    user_message, history
)

# Handle chat responses directly
if intent_type == "chat" and chat_response:
    async for event in self._handle_chat_response(task, user_message, chat_response):
        yield event
    return

# Store percentage for planning
task.task_difficulty_percentage = percentage
```

#### PlannerAgent.process()
```python
# Use thinking budget for complex tasks
if (percentage > self.config.MIN_THINKING_PERCENTAGE and 
    self.ai_client and 
    hasattr(self.ai_client, 'generate_response_with_thinking')):
    response_text = await self.ai_client.generate_response_with_thinking(
        task_id="planner", 
        prompt=prompt, 
        percentage=percentage
    )
else:
    response_text = await self._generate_response(prompt)
```

#### AIClient.generate_response_with_thinking()
```python
def _calculate_thinking_budget(self, percentage: int) -> int:
    if percentage < self.config.MIN_THINKING_PERCENTAGE:
        return 0
    
    # Map 50%-100% to 0-24576
    normalized = (percentage - self.config.MIN_THINKING_PERCENTAGE) / (100 - self.config.MIN_THINKING_PERCENTAGE)
    thinking_budget = int(normalized * 24576)
    return thinking_budget

# Configure generation with thinking
if thinking_budget > 0:
    generate_content_config = types.GenerateContentConfig(
        thinking_config=types.ThinkingConfig(
            thinking_budget=thinking_budget,
        ),
        response_mime_type="text/plain",
    )
```

## Usage Examples

### Chat Intent
**User**: "Hi, how are you?"
**Gemma Response**: `#Chat:Hello! I'm doing well, thank you for asking. How can I help you today?`
**Result**: Direct chat response, no planning or execution

### Simple VPS Task (30%)
**User**: "Check disk space"
**Gemma Response**: `#VPS:30%`
**Result**: Regular planning without thinking budget

### Complex VPS Task (80%)
**User**: "Set up a complete LAMP stack with SSL certificates and configure virtual hosts"
**Gemma Response**: `#VPS:80%`
**Result**: Planning with thinking budget = (80-50)/50 * 24576 = 14,745

## Benefits

1. **Intelligent Routing**: Chat messages get immediate responses without unnecessary planning
2. **Resource Optimization**: Thinking budget only used for complex tasks (>50% difficulty)
3. **Better Planning**: Complex VPS tasks get enhanced AI reasoning capabilities
4. **Scalable**: Percentage-based system allows fine-tuned resource allocation

## Future Enhancements

1. **Dynamic Percentage Adjustment**: Learn from task outcomes to improve percentage estimation
2. **User Feedback Integration**: Allow users to rate task difficulty for model improvement
3. **Cost Optimization**: Track thinking budget usage for cost analysis
4. **Multi-Model Support**: Support different models for different difficulty ranges

## Troubleshooting

### Common Issues

1. **Intent Detection Fails**: Falls back to keyword-based detection
2. **Thinking Budget Unavailable**: Falls back to regular generation
3. **Percentage Parsing Error**: Defaults to 70% difficulty

### Debug Logging

```python
print(f"[Orchestrator] Intent detected: {intent_type}, Percentage: {percentage}%")
print(f"[PlannerAgent] Using thinking budget for {percentage}% difficulty task")
print(f"DEBUG: Percentage {percentage}% -> Thinking budget: {thinking_budget}")
```

## Testing

### Test Cases

1. **Chat Intent**: Verify direct response without planning
2. **Low Difficulty VPS**: Verify regular planning (percentage < 50%)
3. **High Difficulty VPS**: Verify thinking budget usage (percentage > 50%)
4. **Fallback Scenarios**: Verify graceful degradation when components fail

### Example Test Commands

```bash
# Chat test
curl -X POST "http://localhost:8000/api/start" -H "Content-Type: application/json" -d '{"initial_prompt": "Hello, how are you?"}'

# Simple VPS test
curl -X POST "http://localhost:8000/api/start" -H "Content-Type: application/json" -d '{"initial_prompt": "Check disk usage"}'

# Complex VPS test
curl -X POST "http://localhost:8000/api/start" -H "Content-Type: application/json" -d '{"initial_prompt": "Set up a complete web server with database, SSL, and monitoring"}'
```
