"""
Data models for VPS AI Admin Backend.
Contains Pydantic models and data structures.
"""

from typing import Optional, List, Dict, Any
from pydantic import BaseModel
from enum import Enum


class TaskMetadata(BaseModel):
    """Metadata for task creation and tracking."""
    title: Optional[str] = None
    description: Optional[str] = None
    created_at: Optional[str] = None
    priority: Optional[str] = "medium"
    category: Optional[str] = None


class StartRequest(BaseModel):
    """Request model for starting a new task."""
    initial_prompt: str
    task_metadata: Optional[TaskMetadata] = None


class MessageRequest(BaseModel):
    """Request model for sending messages to a task."""
    message: str
    task_id: str


class TaskStatusResponse(BaseModel):
    """Response model for task status information - Enhanced for Orchestrator."""
    task_id: str
    status: str
    history: List[Dict[str, Any]]
    command_to_confirm: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    progress: Optional[float] = 0.0
    step_count: Optional[int] = 0
    total_steps: Optional[int] = 0

    # New orchestrator fields
    use_orchestrator: Optional[bool] = False
    task_plan: Optional[Dict[str, Any]] = None
    main_context_history: Optional[List[str]] = None
    current_step_attempts: Optional[int] = 0
    final_output: Optional[str] = None


class SSHResult(BaseModel):
    """Model for SSH command execution results."""
    stdout: str
    stderr: str
    exit_status: int
    success: bool
    command: Optional[str] = None
    execution_time: Optional[int] = None  # in milliseconds





class StreamEvent(BaseModel):
    """Model for Server-Sent Events."""
    type: str
    content: Any
    metadata: Optional[Dict[str, Any]] = None
    event: str = "message"


# Enhanced models for the Orchestrator system

class TaskStatus(str, Enum):
    """Enhanced task status enumeration."""
    PENDING = "PENDING"
    PLANNING = "PLANNING"
    PLANNED = "PLANNED"
    EXECUTING = "EXECUTING"
    REFINING = "REFINING"
    FAILED = "FAILED"
    COMPLETED = "COMPLETED"
    ABORTED = "ABORTED"


class StepStatus(str, Enum):
    """Status of individual task steps."""
    PENDING = "PENDING"
    EXECUTING = "EXECUTING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    SKIPPED = "SKIPPED"


class TaskStep(BaseModel):
    """Individual step in a task plan."""
    step_number: int
    description: str
    status: StepStatus = StepStatus.PENDING
    command: Optional[str] = None
    attempts: List[Dict[str, Any]] = []
    summary: Optional[str] = None
    created_at: Optional[float] = None
    completed_at: Optional[float] = None


class TaskPlan(BaseModel):
    """Complete task plan with steps."""
    steps: List[TaskStep] = []
    total_steps: int = 0
    current_step: int = 0
    estimated_duration: Optional[int] = None  # in minutes


class StepAttempt(BaseModel):
    """Record of a single step execution attempt."""
    attempt_number: int
    command: str
    ssh_result: Optional[SSHResult] = None
    error_message: Optional[str] = None
    stdout: Optional[str] = None
    stderr: Optional[str] = None
    exit_status: Optional[int] = None
    timestamp: float
    duration_ms: Optional[int] = None
    # Enhanced error recovery fields
    recovery_task_id: Optional[str] = None  # ID of recovery task if one was created
    recovery_completed: bool = False  # Whether recovery was successful
    alternative_approach: Optional[str] = None  # Alternative approach suggested by AI


class AgentResponse(BaseModel):
    """Base model for agent responses."""
    agent_type: str
    content: str
    metadata: Dict[str, Any] = {}
    timestamp: float
    success: bool = True


class PlannerResponse(AgentResponse):
    """Response from the Planner Agent."""
    agent_type: str = "planner"
    plan_steps: List[Dict[str, str]] = []
    estimated_steps: int = 0


class ExecutorResponse(AgentResponse):
    """Response from the Executor Agent."""
    agent_type: str = "executor"
    command: str
    command_type: Optional[str] = None
    security_risk: bool = False


class RefinerResponse(AgentResponse):
    """Response from the Refiner/Debugger Agent."""
    agent_type: str = "refiner"
    original_command: str
    corrected_command: str
    analysis: str


class ErrorRecoveryResponse(AgentResponse):
    """Response from the Error Recovery Planner Agent."""
    agent_type: str = "error_recovery"
    recovery_plan: List[Dict[str, str]] = []
    recovery_approach: str = ""
    estimated_recovery_steps: int = 0
    requires_recursive_planning: bool = True


class SummarizerResponse(AgentResponse):
    """Response from the Summarizer Agent."""
    agent_type: str = "summarizer"
    step_summary: str
    key_outcomes: List[str] = []


class TaskEntry(BaseModel):
    """Internal model for task storage - Enhanced for Orchestrator system."""
    id: str
    title: str
    description: str
    priority: str
    history: List[Dict[str, Any]]
    chat: Optional[Any] = None  # Gemini chat object
    status: str
    command_to_confirm: Optional[str] = None
    system_info: str
    created_at: float

    # Legacy fields (maintained for backward compatibility)
    current_step: int = 0
    estimated_steps: int = 5
    commands_executed: int = 0
    commands_successful: int = 0
    commands_failed: int = 0
    recent_commands: List[str] = []
    metadata: Dict[str, Any] = {}

    # Enhanced error recovery fields
    parent_task_id: Optional[str] = None  # ID of parent task if this is a recovery task
    child_recovery_tasks: List[str] = []  # IDs of child recovery tasks
    is_recovery_task: bool = False  # Whether this task is for error recovery
    failed_step_info: Optional[Dict[str, Any]] = None  # Info about the failed step that triggered recovery

    # New Orchestrator fields
    task_plan: Optional[TaskPlan] = None
    main_context_history: List[str] = []  # Summaries of completed steps
    current_step_attempts: List[StepAttempt] = []  # Attempts for current step only
    original_prompt: str = ""
    final_output: str = ""
    use_orchestrator: bool = True  # Flag to enable new system
    max_retries_per_step: int = 3

    # Orchestrator confirmation state
    awaiting_step_confirmation: bool = False
    current_step_command: Optional[str] = None
    current_step_description: Optional[str] = None
    current_step_number: int = 0
    current_step_attempt: int = 1  # Track current attempt number for error recovery

    # Error recovery state
    in_error_recovery: bool = False
    recovery_plan: List[Dict[str, Any]] = []
    current_recovery_step: int = 0
    failed_step_number: int = 0

    # Intent detection and thinking budget
    task_difficulty_percentage: int = 70  # Default difficulty percentage for thinking budget

    class Config:
        arbitrary_types_allowed = True  # Allow Gemini chat object
